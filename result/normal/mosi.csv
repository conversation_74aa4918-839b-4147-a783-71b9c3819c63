Model,Acc_2,F1_score,Acc_7,<PERSON><PERSON>,Loss
dmd,"(np.float64(82.93), np.float64(0.0))","(np.float64(82.98), np.float64(0.0))","(np.float64(43.59), np.float64(0.0))","(np.float64(75.37), np.float64(0.0))","(np.float64(75.5), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.89), np.float64(0.0))","(np.float64(46.06), np.float64(0.0))","(np.float64(74.94), np.float64(0.0))","(np.float64(74.88), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.81), np.float64(0.0))","(np.float64(44.46), np.float64(0.0))","(np.float64(79.06), np.float64(0.0))","(np.float64(79.12), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.79), np.float64(0.0))","(np.float64(45.48), np.float64(0.0))","(np.float64(77.1), np.float64(0.0))","(np.float64(77.11), np.float64(0.0))"
dmd,"(np.float64(82.62), np.float64(0.0))","(np.float64(82.61), np.float64(0.0))","(np.float64(44.9), np.float64(0.0))","(np.float64(75.7), np.float64(0.0))","(np.float64(75.75), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.89), np.float64(0.0))","(np.float64(46.06), np.float64(0.0))","(np.float64(74.94), np.float64(0.0))","(np.float64(74.88), np.float64(0.0))"
dmd,"(np.float64(84.3), np.float64(0.0))","(np.float64(84.29), np.float64(0.0))","(np.float64(45.34), np.float64(0.0))","(np.float64(72.76), np.float64(0.0))","(np.float64(72.74), np.float64(0.0))"
dmd,"(np.float64(84.3), np.float64(0.0))","(np.float64(84.29), np.float64(0.0))","(np.float64(45.34), np.float64(0.0))","(np.float64(72.76), np.float64(0.0))","(np.float64(72.74), np.float64(0.0))"
dmd,"(np.float64(84.6), np.float64(0.0))","(np.float64(84.62), np.float64(0.0))","(np.float64(44.75), np.float64(0.0))","(np.float64(72.82), np.float64(0.0))","(np.float64(72.76), np.float64(0.0))"
dmd,"(np.float64(84.15), np.float64(0.0))","(np.float64(84.11), np.float64(0.0))","(np.float64(44.61), np.float64(0.0))","(np.float64(74.14), np.float64(0.0))","(np.float64(74.14), np.float64(0.0))"
dmd,"(np.float64(83.54), np.float64(0.0))","(np.float64(83.57), np.float64(0.0))","(np.float64(44.75), np.float64(0.0))","(np.float64(72.6), np.float64(0.0))","(np.float64(72.56), np.float64(0.0))"
